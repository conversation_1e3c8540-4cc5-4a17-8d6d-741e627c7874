^C:\USERS\<USER>\DOCUMENTS\TESTCASE-VIEWER\BUILD\CMAKEFILES\34B19745BCA69BD124BEFCDBB60D74E3\AUTOUIC_(CONFIG).STAMP.RULE
setlocal
cd C:\Users\<USER>\Documents\testcase-viewer\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
^C:\USERS\<USER>\DOCUMENTS\TESTCASE-VIEWER\CMAKELISTS.TXT
setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Documents/testcase-viewer -BC:/Users/<USER>/Documents/testcase-viewer/build --check-stamp-file C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal & call :cmErrorLevel %errorlevel% & goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd
