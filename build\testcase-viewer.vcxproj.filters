﻿<?xml version="1.0" encoding="utf-8"?>
<Project ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\mocs_compilation_Debug.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Documents\testcase-viewer\main.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Documents\testcase-viewer\mainwindow.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Documents\testcase-viewer\testcase.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Documents\testcase-viewer\excelreader.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\mocs_compilation_Release.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\mocs_compilation_MinSizeRel.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <Filter>Source Files</Filter>
    </ClCompile>
  </ItemGroup>
  <ItemGroup>
    <ClInclude Include="C:\Users\<USER>\Documents\testcase-viewer\mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Documents\testcase-viewer\testcase.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Documents\testcase-viewer\excelreader.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_Debug\ui_mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_Release\ui_mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_MinSizeRel\ui_mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
    <ClInclude Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_RelWithDebInfo\ui_mainwindow.h">
      <Filter>Header Files</Filter>
    </ClInclude>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\34b19745bca69bd124befcdbb60d74e3\autouic_(CONFIG).stamp.rule">
      <Filter>CMake Rules</Filter>
    </CustomBuild>
    <CustomBuild Include="C:\Users\<USER>\Documents\testcase-viewer\CMakeLists.txt" />
  </ItemGroup>
  <ItemGroup>
    <None Include="C:\Users\<USER>\Documents\testcase-viewer\mainwindow.ui" />
    <None Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\autouic_Debug.stamp" />
    <None Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\autouic_Release.stamp" />
    <None Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\autouic_MinSizeRel.stamp" />
    <None Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\autouic_RelWithDebInfo.stamp" />
  </ItemGroup>
  <ItemGroup>
    <Filter Include="CMake Rules">
      <UniqueIdentifier>{05E66057-08F1-3630-8FD8-5EE70ADBB507}</UniqueIdentifier>
    </Filter>
    <Filter Include="Header Files">
      <UniqueIdentifier>{91050334-6B87-30F2-BA08-8FC118DEB114}</UniqueIdentifier>
    </Filter>
    <Filter Include="Source Files">
      <UniqueIdentifier>{3AB858D9-6E78-321A-9D49-73FABD7C0AA9}</UniqueIdentifier>
    </Filter>
  </ItemGroup>
</Project>
