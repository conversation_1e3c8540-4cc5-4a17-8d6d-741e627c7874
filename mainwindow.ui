<?xml version="1.0" encoding="UTF-8"?>
<ui version="4.0">
 <class>MainWindow</class>
 <widget class="QMainWindow" name="MainWindow">
  <property name="geometry">
   <rect>
    <x>0</x>
    <y>0</y>
    <width>800</width>
    <height>600</height>
   </rect>
  </property>
  <property name="windowTitle">
   <string>测试用例查看器</string>
  </property>
  <property name="minimumSize">
   <size>
    <width>900</width>
    <height>700</height>
   </size>
  </property>
  <widget class="QWidget" name="centralwidget">
   <layout class="QVBoxLayout" name="mainLayout">
    <property name="spacing">
     <number>10</number>
    </property>
    <property name="leftMargin">
     <number>15</number>
    </property>
    <property name="topMargin">
     <number>15</number>
    </property>
    <property name="rightMargin">
     <number>15</number>
    </property>
    <property name="bottomMargin">
     <number>15</number>
    </property>
    <item>
     <widget class="QGroupBox" name="fileGroupBox">
      <property name="title">
       <string>文件信息</string>
      </property>
      <layout class="QHBoxLayout" name="fileLayout">
       <item>
        <widget class="QPushButton" name="pushButton_5">
         <property name="text">
          <string>打开Excel文件</string>
         </property>
         <property name="minimumSize">
          <size>
           <width>120</width>
           <height>35</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label_4">
         <property name="text">
          <string>当前文件：</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QLabel" name="label">
         <property name="text">
          <string>未选择文件</string>
         </property>
         <property name="styleSheet">
          <string>color: #666;</string>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QGroupBox" name="caseInfoGroupBox">
      <property name="title">
       <string>测试用例信息</string>
      </property>
      <layout class="QVBoxLayout" name="caseInfoLayout">
       <item>
        <layout class="QHBoxLayout" name="caseIdLayout">
         <item>
          <widget class="QLabel" name="label_6">
           <property name="text">
            <string>用例ID：</string>
           </property>
           <property name="minimumSize">
            <size>
             <width>80</width>
             <height>0</height>
            </size>
           </property>
          </widget>
         </item>
         <item>
          <widget class="QLabel" name="label_5">
           <property name="text">
            <string>-</string>
           </property>
           <property name="styleSheet">
            <string>font-weight: bold; color: #2c3e50;</string>
           </property>
          </widget>
         </item>
         <item>
          <spacer name="horizontalSpacer_2">
           <property name="orientation">
            <enum>Qt::Horizontal</enum>
           </property>
           <property name="sizeHint" stdset="0">
            <size>
             <width>40</width>
             <height>20</height>
            </size>
           </property>
          </spacer>
         </item>
         <item>
          <widget class="QPushButton" name="pushButton_2">
           <property name="text">
            <string>复制ID</string>
           </property>
           <property name="minimumSize">
            <size>
             <width>80</width>
             <height>30</height>
            </size>
           </property>
          </widget>
         </item>
        </layout>
       </item>
       <item>
        <layout class="QHBoxLayout" name="contentLayout">
         <item>
          <widget class="QGroupBox" name="stepsGroupBox">
           <property name="title">
            <string>测试步骤</string>
           </property>
           <layout class="QVBoxLayout" name="stepsLayout">
            <item>
             <widget class="QTextBrowser" name="textBrowser">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>200</height>
               </size>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
         <item>
          <widget class="QGroupBox" name="expectedGroupBox">
           <property name="title">
            <string>预期结果</string>
           </property>
           <layout class="QVBoxLayout" name="expectedLayout">
            <item>
             <widget class="QTextBrowser" name="textBrowser_2">
              <property name="minimumSize">
               <size>
                <width>0</width>
                <height>200</height>
               </size>
              </property>
             </widget>
            </item>
           </layout>
          </widget>
         </item>
        </layout>
       </item>
      </layout>
     </widget>
    </item>
    <item>
     <widget class="QGroupBox" name="controlGroupBox">
      <property name="title">
       <string>操作控制</string>
      </property>
      <layout class="QHBoxLayout" name="controlLayout">
       <item>
        <widget class="QPushButton" name="pushButton">
         <property name="text">
          <string>← 上一个</string>
         </property>
         <property name="minimumSize">
          <size>
           <width>100</width>
           <height>40</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_3">
         <property name="text">
          <string>下一个 →</string>
         </property>
         <property name="minimumSize">
          <size>
           <width>100</width>
           <height>40</height>
          </size>
         </property>
        </widget>
       </item>
       <item>
        <spacer name="horizontalSpacer_3">
         <property name="orientation">
          <enum>Qt::Horizontal</enum>
         </property>
         <property name="sizeHint" stdset="0">
          <size>
           <width>40</width>
           <height>20</height>
          </size>
         </property>
        </spacer>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_4">
         <property name="text">
          <string>❌ 失败</string>
         </property>
         <property name="minimumSize">
          <size>
           <width>100</width>
           <height>40</height>
          </size>
         </property>
         <property name="styleSheet">
          <string>QPushButton { background-color: #f8f9fa; border: 2px solid #dc3545; color: #dc3545; border-radius: 5px; }
QPushButton:hover { background-color: #dc3545; color: white; }</string>
         </property>
        </widget>
       </item>
       <item>
        <widget class="QPushButton" name="pushButton_6">
         <property name="text">
          <string>✅ 通过</string>
         </property>
         <property name="minimumSize">
          <size>
           <width>100</width>
           <height>40</height>
          </size>
         </property>
         <property name="styleSheet">
          <string>QPushButton { background-color: #f8f9fa; border: 2px solid #28a745; color: #28a745; border-radius: 5px; }
QPushButton:hover { background-color: #28a745; color: white; }</string>
         </property>
        </widget>
       </item>
      </layout>
     </widget>
    </item>
   </layout>
  </widget>
  <widget class="QMenuBar" name="menubar">
   <property name="geometry">
    <rect>
     <x>0</x>
     <y>0</y>
     <width>800</width>
     <height>17</height>
    </rect>
   </property>
  </widget>
  <widget class="QStatusBar" name="statusbar"/>
 </widget>
 <resources/>
 <connections/>
</ui>
