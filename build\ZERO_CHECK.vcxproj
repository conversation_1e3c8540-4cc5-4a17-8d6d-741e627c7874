﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <PropertyGroup>
    <ResolveNugetPackages>false</ResolveNugetPackages>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{6EAA0E62-FC10-3C83-B875-EAB60F234180}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>ZERO_CHECK</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Utility</ConfigurationType>
    <CharacterSet>MultiByte</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">$(Platform)\$(Configuration)\$(ProjectName)\</IntDir>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <Midl>
      <AdditionalIncludeDirectories>%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\a855e813f14170deec04e1533d5e0d0e\generate.stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Documents/testcase-viewer -BC:/Users/<USER>/Documents/testcase-viewer/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Documents/testcase-viewer/build/testcase-viewer.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Documents\testcase-viewer\CMakeLists.txt;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeCXXCompiler.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeRCCompiler.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeSystem.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\FastFloatConfig.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\FastFloatConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\fast_float-targets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\FindWrapAtomic.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Config.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigExtras.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Dependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Targets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6VersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtFeature.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtFeatureCommon.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtInstallPaths.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicAppleHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicCMakeHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicDependencyHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicExternalProjectHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicFinalizerHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicFindPackageHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicGitHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicPluginHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomCpeHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomDepHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomFileHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomOpsHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomPurlHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomPythonHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicTargetHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicTestHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicToolHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicWalkLibsHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigExtras.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreMacros.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiPlugins.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppConfig.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppTargets.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntConfig.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets.cmake;C:\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Documents/testcase-viewer -BC:/Users/<USER>/Documents/testcase-viewer/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Documents/testcase-viewer/build/testcase-viewer.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Documents\testcase-viewer\CMakeLists.txt;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeCXXCompiler.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeRCCompiler.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeSystem.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\FastFloatConfig.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\FastFloatConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\fast_float-targets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\FindWrapAtomic.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Config.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigExtras.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Dependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Targets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6VersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtFeature.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtFeatureCommon.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtInstallPaths.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicAppleHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicCMakeHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicDependencyHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicExternalProjectHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicFinalizerHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicFindPackageHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicGitHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicPluginHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomCpeHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomDepHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomFileHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomOpsHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomPurlHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomPythonHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicTargetHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicTestHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicToolHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicWalkLibsHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigExtras.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreMacros.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiPlugins.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppConfig.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppTargets.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntConfig.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets.cmake;C:\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Documents/testcase-viewer -BC:/Users/<USER>/Documents/testcase-viewer/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Documents/testcase-viewer/build/testcase-viewer.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Documents\testcase-viewer\CMakeLists.txt;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeCXXCompiler.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeRCCompiler.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeSystem.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\FastFloatConfig.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\FastFloatConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\fast_float-targets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\FindWrapAtomic.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Config.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigExtras.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Dependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Targets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6VersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtFeature.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtFeatureCommon.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtInstallPaths.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicAppleHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicCMakeHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicDependencyHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicExternalProjectHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicFinalizerHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicFindPackageHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicGitHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicPluginHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomCpeHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomDepHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomFileHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomOpsHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomPurlHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomPythonHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicTargetHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicTestHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicToolHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicWalkLibsHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigExtras.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreMacros.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiPlugins.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppConfig.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppTargets.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntConfig.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets.cmake;C:\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Checking Build System</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Documents/testcase-viewer -BC:/Users/<USER>/Documents/testcase-viewer/build --check-stamp-list CMakeFiles/generate.stamp.list --vs-solution-file C:/Users/<USER>/Documents/testcase-viewer/build/testcase-viewer.sln
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Documents\testcase-viewer\CMakeLists.txt;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeCXXCompiler.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeRCCompiler.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeSystem.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\FastFloatConfig.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\FastFloatConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\fast_float-targets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\FindWrapAtomic.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Config.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigExtras.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Dependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Targets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6VersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtFeature.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtFeatureCommon.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtInstallPaths.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicAppleHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicCMakeHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicDependencyHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicExternalProjectHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicFinalizerHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicFindPackageHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicGitHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicPluginHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomCpeHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomDepHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomFileHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomOpsHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomPurlHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomPythonHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicTargetHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicTestHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicToolHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicWalkLibsHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigExtras.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreMacros.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiPlugins.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppConfig.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppTargets.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntConfig.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets.cmake;C:\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <ItemGroup>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>