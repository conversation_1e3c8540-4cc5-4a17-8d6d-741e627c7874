#include "mainwindow.h"
#include "ui_mainwindow.h"
#include <QDebug>

MainWindow::MainWindow(QWidget *parent)
    : QMainWindow(parent)
    , ui(new Ui::MainWindow)
{
    ui->setupUi(this);

    // 设置窗口标题
    setWindowTitle("测试用例查看器 v1.0");

    // 连接信号和槽
    connect(ui->pushButton_5, &QPushButton::clicked, this, &MainWindow::onOpenExcelClicked);
    connect(ui->pushButton, &QPushButton::clicked, this, &MainWindow::onPreviousClicked);
    connect(ui->pushButton_3, &QPushButton::clicked, this, &MainWindow::onNextClicked);
    connect(ui->pushButton_2, &QPushButton::clicked, this, &MainWindow::onCopyIdClicked);
    connect(ui->pushButton_6, &QPushButton::clicked, this, &MainWindow::onPassedClicked);
    connect(ui->pushButton_4, &QPushButton::clicked, this, &MainWindow::onFailedClicked);

    // 初始化UI
    updateUI();
}

MainWindow::~MainWindow()
{
    delete ui;
}

void MainWindow::onOpenExcelClicked()
{
    QString filePath = QFileDialog::getOpenFileName(
        this,
        "选择Excel文件",
        "",
        "Excel文件 (*.xlsx *.xls)"
    );

    if (filePath.isEmpty()) {
        return;
    }

    // 保存当前结果（如果有的话）
    if (m_excelReader.isFileLoaded()) {
        saveCurrentResults();
    }

    // 读取新的Excel文件
    if (m_excelReader.readExcelFile(filePath)) {
        m_currentFilePath = filePath;

        // 清空当前测试用例管理器
        m_testCaseManager.clearTestCases();

        // 添加新的测试用例
        QList<TestCase> testCases = m_excelReader.getTestCases();
        for (const TestCase &testCase : testCases) {
            m_testCaseManager.addTestCase(testCase);
        }

        showMessage(QString("成功加载 %1 个测试用例").arg(testCases.size()));
        updateUI();
    } else {
        showMessage(QString("加载Excel文件失败: %1").arg(m_excelReader.getLastError()), true);
    }
}

void MainWindow::onPreviousClicked()
{
    if (m_testCaseManager.moveToPrevious()) {
        updateUI();
    }
}

void MainWindow::onNextClicked()
{
    if (m_testCaseManager.moveToNext()) {
        updateUI();
    }
}

void MainWindow::onCopyIdClicked()
{
    const TestCase* currentCase = m_testCaseManager.getCurrentTestCase();
    if (currentCase) {
        QClipboard* clipboard = QApplication::clipboard();
        clipboard->setText(currentCase->getCaseId());
        showMessage("用例ID已复制到剪贴板");
    }
}

void MainWindow::onPassedClicked()
{
    m_testCaseManager.setCurrentTestResult(TestCase::Passed);
    updateUI();
    showMessage("测试结果已标记为通过");
}

void MainWindow::onFailedClicked()
{
    m_testCaseManager.setCurrentTestResult(TestCase::Failed);
    updateUI();
    showMessage("测试结果已标记为失败");
}

void MainWindow::updateUI()
{
    const TestCase* currentCase = m_testCaseManager.getCurrentTestCase();

    if (currentCase) {
        // 更新用例信息显示
        ui->label_5->setText(currentCase->getCaseId());
        ui->textBrowser->setPlainText(currentCase->getTestSteps());
        ui->textBrowser_2->setPlainText(currentCase->getExpectedResult());

        // 更新测试结果按钮状态
        QString resultText;
        switch (currentCase->getTestResult()) {
            case TestCase::Passed:
                resultText = "通过";
                ui->pushButton_6->setStyleSheet("background-color: lightgreen;");
                ui->pushButton_4->setStyleSheet("");
                break;
            case TestCase::Failed:
                resultText = "失败";
                ui->pushButton_4->setStyleSheet("background-color: lightcoral;");
                ui->pushButton_6->setStyleSheet("");
                break;
            case TestCase::NotTested:
            default:
                resultText = "未测试";
                ui->pushButton_6->setStyleSheet("");
                ui->pushButton_4->setStyleSheet("");
                break;
        }

        // 显示当前进度
        int current = m_testCaseManager.getCurrentIndex() + 1;
        int total = m_testCaseManager.getTestCaseCount();
        setWindowTitle(QString("测试用例查看器 v1.0 - %1/%2 [%3]").arg(current).arg(total).arg(resultText));

    } else {
        // 没有测试用例时的显示
        ui->label_5->setText("无");
        ui->textBrowser->clear();
        ui->textBrowser_2->clear();
        ui->pushButton_6->setStyleSheet("");
        ui->pushButton_4->setStyleSheet("");
        setWindowTitle("测试用例查看器 v1.0");
    }

    // 更新文件路径显示
    if (!m_currentFilePath.isEmpty()) {
        QFileInfo fileInfo(m_currentFilePath);
        ui->label->setText(fileInfo.fileName());
    } else {
        ui->label->setText("未选择文件");
    }

    updateNavigationButtons();
}

void MainWindow::updateNavigationButtons()
{
    bool hasTestCases = !m_testCaseManager.isEmpty();
    bool hasPrevious = m_testCaseManager.hasPrevious();
    bool hasNext = m_testCaseManager.hasNext();

    ui->pushButton->setEnabled(hasTestCases && hasPrevious);     // 上一个
    ui->pushButton_3->setEnabled(hasTestCases && hasNext);      // 下一个
    ui->pushButton_2->setEnabled(hasTestCases);                 // 复制ID
    ui->pushButton_6->setEnabled(hasTestCases);                 // 通过
    ui->pushButton_4->setEnabled(hasTestCases);                 // 失败
}

void MainWindow::showMessage(const QString &message, bool isError)
{
    if (isError) {
        QMessageBox::warning(this, "错误", message);
    } else {
        ui->statusbar->showMessage(message, 3000); // 显示3秒
    }
}

void MainWindow::saveCurrentResults()
{
    if (m_excelReader.isFileLoaded() && !m_testCaseManager.isEmpty()) {
        // 更新Excel读取器中的测试用例数据
        m_excelReader.updateTestCases(m_testCaseManager.getAllTestCases());

        // 保存到原文件
        if (m_excelReader.saveExcelFile()) {
            qDebug() << "测试结果已自动保存";
        } else {
            qDebug() << "保存测试结果失败:" << m_excelReader.getLastError();
        }
    }
}
