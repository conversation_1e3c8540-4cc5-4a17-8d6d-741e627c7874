#ifndef MAINWINDOW_H
#define MAINWINDOW_H

#include <QMainWindow>
#include <QFileDialog>
#include <QMessageBox>
#include <QClipboard>
#include <QApplication>
#include "testcase.h"
#include "excelreader.h"

namespace Ui {
class MainWindow;
}

class MainWindow : public QMainWindow
{
    Q_OBJECT

public:
    explicit MainWindow(QWidget *parent = nullptr);
    ~MainWindow();

private slots:
    void onOpenExcelClicked();
    void onPreviousClicked();
    void onNextClicked();
    void onCopyIdClicked();
    void onPassedClicked();
    void onFailedClicked();

private:
    void updateUI();
    void updateNavigationButtons();
    void showMessage(const QString &message, bool isError = false);
    void saveCurrentResults();

private:
    Ui::MainWindow *ui;
    TestCaseManager m_testCaseManager;
    ExcelReader m_excelReader;
    QString m_currentFilePath;
};

#endif // MAINWINDOW_H
