
---
events:
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineSystem.cmake:233 (message)"
      - "CMakeLists.txt:3 (project)"
    message: |
      The system is: Windows - 10.0.26100 - AMD64
  -
    kind: "message-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:17 (message)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerId.cmake:64 (__determine_compiler_id_test)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCXXCompiler.cmake:126 (CMAKE_DETERMINE_COMPILER_ID)"
      - "CMakeLists.txt:3 (project)"
    message: |
      Compiling the CXX compiler identification source file "CMakeCXXCompilerId.cpp" succeeded.
      Compiler:  
      Build flags: 
      Id flags:  
      
      The output was:
      0
      閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
      鐢熸垚鍚姩鏃堕棿涓?2025/6/30 19:21:51銆?
      
      鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\3.28.6\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)銆?
      PrepareForBuild:
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\鈥濄€?
        宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
        姝ｅ湪鍒涘缓鐩綍鈥淒ebug\\CompilerIdCXX.tlog\\鈥濄€?
      InitializeBuildStatus:
        姝ｅ湪鍒涘缓鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
      ClCompile:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /nologo /W0 /WX- /diagnostics:column /Od /D _MBCS /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /Fo"Debug\\\\" /Fd"Debug\\vc143.pdb" /external:W0 /Gd /TP /FC /errorReport:queue CMakeCXXCompilerId.cpp
        CMakeCXXCompilerId.cpp
      Link:
        C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:".\\CompilerIdCXX.exe" /INCREMENTAL:NO /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib comdlg32.lib advapi32.lib shell32.lib ole32.lib oleaut32.lib uuid.lib odbc32.lib odbccp32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /PDB:".\\CompilerIdCXX.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:".\\CompilerIdCXX.lib" /MACHINE:X64 Debug\\CMakeCXXCompilerId.obj
        CompilerIdCXX.vcxproj -> C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\3.28.6\\CompilerIdCXX\\CompilerIdCXX.exe
      PostBuildEvent:
        for %%i in (cl.exe) do @echo CMAKE_CXX_COMPILER=%%~$PATH:i
        :VCEnd
        CMAKE_CXX_COMPILER=C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\Hostx64\\x64\\cl.exe
      FinalizeBuildStatus:
        姝ｅ湪鍒犻櫎鏂囦欢鈥淒ebug\\CompilerIdCXX.tlog\\unsuccessfulbuild鈥濄€?
        姝ｅ湪瀵光€淒ebug\\CompilerIdCXX.tlog\\CompilerIdCXX.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
      宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\3.28.6\\CompilerIdCXX\\CompilerIdCXX.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
      
      宸叉垚鍔熺敓鎴愩€?
          0 涓鍛?
          0 涓敊璇?
      
      宸茬敤鏃堕棿 00:00:00.38
      
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.exe"
      
      Compilation of the CXX compiler identification source "CMakeCXXCompilerId.cpp" produced "CompilerIdCXX.vcxproj"
      
      The CXX compiler identification is MSVC, found in:
        C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/3.28.6/CompilerIdCXX/CompilerIdCXX.exe
      
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeDetermineCompilerABI.cmake:60 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeTestCXXCompiler.cmake:26 (CMAKE_DETERMINE_COMPILER_ABI)"
      - "CMakeLists.txt:3 (project)"
    checks:
      - "Detecting CXX compiler ABI info"
    directories:
      source: "C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-rkdimt"
      binary: "C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-rkdimt"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_CXX_SCAN_FOR_MODULES: "OFF"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
    buildResult:
      variable: "CMAKE_CXX_ABI_COMPILED"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-rkdimt'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_21082.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/6/30 19:21:51銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rkdimt\\cmTC_21082.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_21082.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rkdimt\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_21082.dir\\Debug\\cmTC_21082.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_21082.dir\\Debug\\cmTC_21082.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_21082.dir\\Debug\\cmTC_21082.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_21082.dir\\Debug\\\\" /Fd"cmTC_21082.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.28\\Modules\\CMakeCXXCompilerABI.cpp"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /Fo"cmTC_21082.dir\\Debug\\\\" /Fd"cmTC_21082.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Program Files\\CMake\\share\\cmake-3.28\\Modules\\CMakeCXXCompilerABI.cpp"
          CMakeCXXCompilerABI.cpp
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rkdimt\\Debug\\cmTC_21082.exe" /INCREMENTAL /ILK:"cmTC_21082.dir\\Debug\\cmTC_21082.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-rkdimt/Debug/cmTC_21082.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-rkdimt/Debug/cmTC_21082.lib" /MACHINE:X64  /machine:x64 cmTC_21082.dir\\Debug\\CMakeCXXCompilerABI.obj
          cmTC_21082.vcxproj -> C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rkdimt\\Debug\\cmTC_21082.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_21082.dir\\Debug\\cmTC_21082.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_21082.dir\\Debug\\cmTC_21082.tlog\\cmTC_21082.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-rkdimt\\cmTC_21082.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.38
        
      exitCode: 0
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CheckCXXSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindThreads.cmake:99 (CHECK_CXX_SOURCE_COMPILES)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindThreads.cmake:163 (_threads_check_libc)"
      - "C:/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/vcpkg/installed/x64-windows/share/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/vcpkg/installed/x64-windows/share/Qt6/Qt6Dependencies.cmake:34 (_qt_internal_find_third_party_dependencies)"
      - "C:/vcpkg/installed/x64-windows/share/Qt6/Qt6Config.cmake:146 (include)"
      - "C:/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "CMakeLists.txt:12 (find_package)"
    checks:
      - "Performing Test CMAKE_HAVE_LIBC_PTHREAD"
    directories:
      source: "C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-d60nhd"
      binary: "C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-d60nhd"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/vcpkg/installed/x64-windows/share/Qt6;C:/vcpkg/installed/x64-windows/share/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/vcpkg/installed/x64-windows/share/Qt6/3rdparty/kwin"
      VCPKG_INSTALLED_DIR: "C:/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/vcpkg"
    buildResult:
      variable: "CMAKE_HAVE_LIBC_PTHREAD"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-d60nhd'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_21461.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/6/30 19:21:52銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-d60nhd\\cmTC_21461.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_21461.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-d60nhd\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_21461.dir\\Debug\\cmTC_21461.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_21461.dir\\Debug\\cmTC_21461.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_21461.dir\\Debug\\cmTC_21461.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_21461.dir\\Debug\\\\" /Fd"cmTC_21461.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-d60nhd\\src.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CMAKE_HAVE_LIBC_PTHREAD /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_21461.dir\\Debug\\\\" /Fd"cmTC_21461.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-d60nhd\\src.cxx"
          src.cxx
        C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-d60nhd\\src.cxx(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減thread.h鈥? No such file or directory [C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-d60nhd\\cmTC_21461.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-d60nhd\\cmTC_21461.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-d60nhd\\cmTC_21461.vcxproj鈥?榛樿鐩爣) (1) ->
        (ClCompile 鐩爣) -> 
          C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-d60nhd\\src.cxx(1,10): error C1083: 鏃犳硶鎵撳紑鍖呮嫭鏂囦欢: 鈥減thread.h鈥? No such file or directory [C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-d60nhd\\cmTC_21461.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.25
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindThreads.cmake:175 (_threads_check_lib)"
      - "C:/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/vcpkg/installed/x64-windows/share/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/vcpkg/installed/x64-windows/share/Qt6/Qt6Dependencies.cmake:34 (_qt_internal_find_third_party_dependencies)"
      - "C:/vcpkg/installed/x64-windows/share/Qt6/Qt6Config.cmake:146 (include)"
      - "C:/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "CMakeLists.txt:12 (find_package)"
    checks:
      - "Looking for pthread_create in pthreads"
    directories:
      source: "C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-kl718x"
      binary: "C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-kl718x"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/vcpkg/installed/x64-windows/share/Qt6;C:/vcpkg/installed/x64-windows/share/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/vcpkg/installed/x64-windows/share/Qt6/3rdparty/kwin"
      VCPKG_INSTALLED_DIR: "C:/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/vcpkg"
    buildResult:
      variable: "CMAKE_HAVE_PTHREADS_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-kl718x'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_91ef8.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/6/30 19:21:53銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kl718x\\cmTC_91ef8.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_91ef8.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kl718x\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_91ef8.dir\\Debug\\cmTC_91ef8.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_91ef8.dir\\Debug\\cmTC_91ef8.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_91ef8.dir\\Debug\\cmTC_91ef8.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_91ef8.dir\\Debug\\\\" /Fd"cmTC_91ef8.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kl718x\\CheckFunctionExists.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_91ef8.dir\\Debug\\\\" /Fd"cmTC_91ef8.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kl718x\\CheckFunctionExists.cxx"
          CheckFunctionExists.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kl718x\\Debug\\cmTC_91ef8.exe" /INCREMENTAL /ILK:"cmTC_91ef8.dir\\Debug\\cmTC_91ef8.ilk" /NOLOGO pthreads.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-kl718x/Debug/cmTC_91ef8.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-kl718x/Debug/cmTC_91ef8.lib" /MACHINE:X64  /machine:x64 cmTC_91ef8.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減threads.lib鈥?[C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kl718x\\cmTC_91ef8.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kl718x\\cmTC_91ef8.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kl718x\\cmTC_91ef8.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減threads.lib鈥?[C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-kl718x\\cmTC_91ef8.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.28
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CheckLibraryExists.cmake:69 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindThreads.cmake:112 (CHECK_LIBRARY_EXISTS)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/FindThreads.cmake:176 (_threads_check_lib)"
      - "C:/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/vcpkg/installed/x64-windows/share/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/vcpkg/installed/x64-windows/share/Qt6/Qt6Dependencies.cmake:34 (_qt_internal_find_third_party_dependencies)"
      - "C:/vcpkg/installed/x64-windows/share/Qt6/Qt6Config.cmake:146 (include)"
      - "C:/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "CMakeLists.txt:12 (find_package)"
    checks:
      - "Looking for pthread_create in pthread"
    directories:
      source: "C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-bnlbqi"
      binary: "C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-bnlbqi"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/vcpkg/installed/x64-windows/share/Qt6;C:/vcpkg/installed/x64-windows/share/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/vcpkg/installed/x64-windows/share/Qt6/3rdparty/kwin"
      VCPKG_INSTALLED_DIR: "C:/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/vcpkg"
    buildResult:
      variable: "CMAKE_HAVE_PTHREAD_CREATE"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-bnlbqi'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_1045b.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/6/30 19:21:53銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bnlbqi\\cmTC_1045b.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_1045b.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bnlbqi\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_1045b.dir\\Debug\\cmTC_1045b.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_1045b.dir\\Debug\\cmTC_1045b.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_1045b.dir\\Debug\\cmTC_1045b.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_1045b.dir\\Debug\\\\" /Fd"cmTC_1045b.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bnlbqi\\CheckFunctionExists.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D CHECK_FUNCTION_EXISTS=pthread_create /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_1045b.dir\\Debug\\\\" /Fd"cmTC_1045b.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bnlbqi\\CheckFunctionExists.cxx"
          CheckFunctionExists.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bnlbqi\\Debug\\cmTC_1045b.exe" /INCREMENTAL /ILK:"cmTC_1045b.dir\\Debug\\cmTC_1045b.ilk" /NOLOGO pthread.lib kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-bnlbqi/Debug/cmTC_1045b.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-bnlbqi/Debug/cmTC_1045b.lib" /MACHINE:X64  /machine:x64 cmTC_1045b.dir\\Debug\\CheckFunctionExists.obj
        LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減thread.lib鈥?[C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bnlbqi\\cmTC_1045b.vcxproj]
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bnlbqi\\cmTC_1045b.vcxproj鈥?榛樿鐩爣)鐨勬搷浣?- 澶辫触銆?
        
        鐢熸垚澶辫触銆?
        
        鈥淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bnlbqi\\cmTC_1045b.vcxproj鈥?榛樿鐩爣) (1) ->
        (Link 鐩爣) -> 
          LINK : fatal error LNK1104: 鏃犳硶鎵撳紑鏂囦欢鈥減thread.lib鈥?[C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-bnlbqi\\cmTC_1045b.vcxproj]
        
            0 涓鍛?
            1 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.29
        
      exitCode: 1
  -
    kind: "try_compile-v1"
    backtrace:
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/Internal/CheckSourceCompiles.cmake:101 (try_compile)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CheckCXXSourceCompiles.cmake:52 (cmake_check_source_compiles)"
      - "C:/vcpkg/installed/x64-windows/share/Qt6/FindWrapAtomic.cmake:36 (check_cxx_source_compiles)"
      - "C:/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "C:/Program Files/CMake/share/cmake-3.28/Modules/CMakeFindDependencyMacro.cmake:76 (find_package)"
      - "C:/vcpkg/installed/x64-windows/share/Qt6/QtPublicDependencyHelpers.cmake:36 (find_dependency)"
      - "C:/vcpkg/installed/x64-windows/share/Qt6Core/Qt6CoreDependencies.cmake:35 (_qt_internal_find_third_party_dependencies)"
      - "C:/vcpkg/installed/x64-windows/share/Qt6Core/Qt6CoreConfig.cmake:45 (include)"
      - "C:/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "C:/vcpkg/installed/x64-windows/share/Qt6/Qt6Config.cmake:196 (find_package)"
      - "C:/vcpkg/scripts/buildsystems/vcpkg.cmake:896 (_find_package)"
      - "CMakeLists.txt:12 (find_package)"
    checks:
      - "Performing Test HAVE_STDATOMIC"
    directories:
      source: "C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-43onrt"
      binary: "C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-43onrt"
    cmakeVariables:
      CMAKE_CXX_FLAGS: "/DWIN32 /D_WINDOWS /GR /EHsc"
      CMAKE_CXX_FLAGS_DEBUG: "/Zi /Ob0 /Od /RTC1"
      CMAKE_EXE_LINKER_FLAGS: "/machine:x64"
      CMAKE_MODULE_PATH: "C:/vcpkg/installed/x64-windows/share/Qt6;C:/vcpkg/installed/x64-windows/share/Qt6/3rdparty/extra-cmake-modules/find-modules;C:/vcpkg/installed/x64-windows/share/Qt6/3rdparty/kwin"
      VCPKG_INSTALLED_DIR: "C:/vcpkg/installed"
      VCPKG_PREFER_SYSTEM_LIBS: "OFF"
      VCPKG_TARGET_TRIPLET: "x64-windows"
      Z_VCPKG_ROOT_DIR: "C:/vcpkg"
    buildResult:
      variable: "HAVE_STDATOMIC"
      cached: true
      stdout: |
        Change Dir: 'C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-43onrt'
        
        Run Build Command(s): "C:/Program Files/Microsoft Visual Studio/2022/Community/MSBuild/Current/Bin/amd64/MSBuild.exe" cmTC_23a4d.vcxproj /p:Configuration=Debug /p:Platform=x64 /p:VisualStudioVersion=17.0 /v:n
        閫傜敤浜?.NET Framework MSBuild 鐗堟湰 17.14.14+a129329f1
        鐢熸垚鍚姩鏃堕棿涓?2025/6/30 19:21:54銆?
        
        鑺傜偣 1 涓婄殑椤圭洰鈥淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-43onrt\\cmTC_23a4d.vcxproj鈥?榛樿鐩爣)銆?
        PrepareForBuild:
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_23a4d.dir\\Debug\\鈥濄€?
          宸插惎鐢ㄧ粨鏋勫寲杈撳嚭銆傜紪璇戝櫒璇婃柇鐨勬牸寮忚缃皢鍙嶆槧閿欒灞傛缁撴瀯銆傛湁鍏宠缁嗕俊鎭紝璇峰弬闃?https://aka.ms/cpp/structured-output銆?
          姝ｅ湪鍒涘缓鐩綍鈥淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-43onrt\\Debug\\鈥濄€?
          姝ｅ湪鍒涘缓鐩綍鈥渃mTC_23a4d.dir\\Debug\\cmTC_23a4d.tlog\\鈥濄€?
        InitializeBuildStatus:
          姝ｅ湪鍒涘缓鈥渃mTC_23a4d.dir\\Debug\\cmTC_23a4d.tlog\\unsuccessfulbuild鈥濓紝鍥犱负宸叉寚瀹氣€淎lwaysCreate鈥濄€?
          姝ｅ湪瀵光€渃mTC_23a4d.dir\\Debug\\cmTC_23a4d.tlog\\unsuccessfulbuild鈥濇墽琛?Touch 浠诲姟銆?
        ClCompile:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\CL.exe /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_STDATOMIC /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_23a4d.dir\\Debug\\\\" /Fd"cmTC_23a4d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-43onrt\\src.cxx"
          鐢ㄤ簬 x64 鐨?Microsoft (R) C/C++ 浼樺寲缂栬瘧鍣?19.44.35211 鐗?
          鐗堟潈鎵€鏈?C) Microsoft Corporation銆備繚鐣欐墍鏈夋潈鍒┿€?
          cl /c /Zi /W1 /WX- /diagnostics:column /Od /Ob0 /D _MBCS /D WIN32 /D _WINDOWS /D HAVE_STDATOMIC /D "CMAKE_INTDIR=\\"Debug\\"" /Gm- /EHsc /RTC1 /MDd /GS /fp:precise /Zc:wchar_t /Zc:forScope /Zc:inline /GR /std:c++17 /Fo"cmTC_23a4d.dir\\Debug\\\\" /Fd"cmTC_23a4d.dir\\Debug\\vc143.pdb" /external:W1 /Gd /TP /errorReport:queue "C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-43onrt\\src.cxx"
          src.cxx
        Link:
          C:\\Program Files\\Microsoft Visual Studio\\2022\\Community\\VC\\Tools\\MSVC\\14.44.35207\\bin\\HostX64\\x64\\link.exe /ERRORREPORT:QUEUE /OUT:"C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-43onrt\\Debug\\cmTC_23a4d.exe" /INCREMENTAL /ILK:"cmTC_23a4d.dir\\Debug\\cmTC_23a4d.ilk" /NOLOGO kernel32.lib user32.lib gdi32.lib winspool.lib shell32.lib ole32.lib oleaut32.lib uuid.lib comdlg32.lib advapi32.lib /MANIFEST /MANIFESTUAC:"level='asInvoker' uiAccess='false'" /manifest:embed /DEBUG /PDB:"C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-43onrt/Debug/cmTC_23a4d.pdb" /SUBSYSTEM:CONSOLE /TLBID:1 /DYNAMICBASE /NXCOMPAT /IMPLIB:"C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/CMakeScratch/TryCompile-43onrt/Debug/cmTC_23a4d.lib" /MACHINE:X64  /machine:x64 cmTC_23a4d.dir\\Debug\\src.obj
          cmTC_23a4d.vcxproj -> C:\\Users\\<USER>\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-43onrt\\Debug\\cmTC_23a4d.exe
        FinalizeBuildStatus:
          姝ｅ湪鍒犻櫎鏂囦欢鈥渃mTC_23a4d.dir\\Debug\\cmTC_23a4d.tlog\\unsuccessfulbuild鈥濄€?
          姝ｅ湪瀵光€渃mTC_23a4d.dir\\Debug\\cmTC_23a4d.tlog\\cmTC_23a4d.lastbuildstate鈥濇墽琛?Touch 浠诲姟銆?
        宸插畬鎴愮敓鎴愰」鐩€淐:\\Users\\wps\\Documents\\testcase-viewer\\build\\CMakeFiles\\CMakeScratch\\TryCompile-43onrt\\cmTC_23a4d.vcxproj鈥?榛樿鐩爣)鐨勬搷浣溿€?
        
        宸叉垚鍔熺敓鎴愩€?
            0 涓鍛?
            0 涓敊璇?
        
        宸茬敤鏃堕棿 00:00:00.39
        
      exitCode: 0
...
