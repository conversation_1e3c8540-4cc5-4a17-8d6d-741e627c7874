/********************************************************************************
** Form generated from reading UI file 'mainwindow.ui'
**
** Created by: Qt User Interface Compiler version 6.8.3
**
** WARNING! All changes made in this file will be lost when recompiling UI file!
********************************************************************************/

#ifndef UI_MAINWINDOW_H
#define UI_MAINWINDOW_H

#include <QtCore/QVariant>
#include <QtWidgets/QApplication>
#include <QtWidgets/QGroupBox>
#include <QtWidgets/QHBoxLayout>
#include <QtWidgets/QLabel>
#include <QtWidgets/QMainWindow>
#include <QtWidgets/QMenuBar>
#include <QtWidgets/QPushButton>
#include <QtWidgets/QSpacerItem>
#include <QtWidgets/QStatusBar>
#include <QtWidgets/QTextBrowser>
#include <QtWidgets/QVBoxLayout>
#include <QtWidgets/QWidget>

QT_BEGIN_NAMESPACE

class Ui_MainWindow
{
public:
    QWidget *centralwidget;
    QVBoxLayout *mainLayout;
    QGroupBox *fileGroupBox;
    QHBoxLayout *fileLayout;
    QPushButton *pushButton_5;
    QLabel *label_4;
    QLabel *label;
    QSpacerItem *horizontalSpacer;
    QGroupBox *caseInfoGroupBox;
    QVBoxLayout *caseInfoLayout;
    QHBoxLayout *caseIdLayout;
    QLabel *label_6;
    QLabel *label_5;
    QSpacerItem *horizontalSpacer_2;
    QPushButton *pushButton_2;
    QHBoxLayout *contentLayout;
    QGroupBox *stepsGroupBox;
    QVBoxLayout *stepsLayout;
    QTextBrowser *textBrowser;
    QGroupBox *expectedGroupBox;
    QVBoxLayout *expectedLayout;
    QTextBrowser *textBrowser_2;
    QGroupBox *controlGroupBox;
    QHBoxLayout *controlLayout;
    QPushButton *pushButton;
    QPushButton *pushButton_3;
    QSpacerItem *horizontalSpacer_3;
    QPushButton *pushButton_4;
    QPushButton *pushButton_6;
    QMenuBar *menubar;
    QStatusBar *statusbar;

    void setupUi(QMainWindow *MainWindow)
    {
        if (MainWindow->objectName().isEmpty())
            MainWindow->setObjectName("MainWindow");
        MainWindow->resize(800, 600);
        MainWindow->setMinimumSize(QSize(900, 700));
        centralwidget = new QWidget(MainWindow);
        centralwidget->setObjectName("centralwidget");
        mainLayout = new QVBoxLayout(centralwidget);
        mainLayout->setSpacing(10);
        mainLayout->setObjectName("mainLayout");
        mainLayout->setContentsMargins(15, 15, 15, 15);
        fileGroupBox = new QGroupBox(centralwidget);
        fileGroupBox->setObjectName("fileGroupBox");
        fileLayout = new QHBoxLayout(fileGroupBox);
        fileLayout->setObjectName("fileLayout");
        pushButton_5 = new QPushButton(fileGroupBox);
        pushButton_5->setObjectName("pushButton_5");
        pushButton_5->setMinimumSize(QSize(120, 35));

        fileLayout->addWidget(pushButton_5);

        label_4 = new QLabel(fileGroupBox);
        label_4->setObjectName("label_4");

        fileLayout->addWidget(label_4);

        label = new QLabel(fileGroupBox);
        label->setObjectName("label");

        fileLayout->addWidget(label);

        horizontalSpacer = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        fileLayout->addItem(horizontalSpacer);


        mainLayout->addWidget(fileGroupBox);

        caseInfoGroupBox = new QGroupBox(centralwidget);
        caseInfoGroupBox->setObjectName("caseInfoGroupBox");
        caseInfoLayout = new QVBoxLayout(caseInfoGroupBox);
        caseInfoLayout->setObjectName("caseInfoLayout");
        caseIdLayout = new QHBoxLayout();
        caseIdLayout->setObjectName("caseIdLayout");
        label_6 = new QLabel(caseInfoGroupBox);
        label_6->setObjectName("label_6");
        label_6->setMinimumSize(QSize(80, 0));

        caseIdLayout->addWidget(label_6);

        label_5 = new QLabel(caseInfoGroupBox);
        label_5->setObjectName("label_5");

        caseIdLayout->addWidget(label_5);

        horizontalSpacer_2 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        caseIdLayout->addItem(horizontalSpacer_2);

        pushButton_2 = new QPushButton(caseInfoGroupBox);
        pushButton_2->setObjectName("pushButton_2");
        pushButton_2->setMinimumSize(QSize(80, 30));

        caseIdLayout->addWidget(pushButton_2);


        caseInfoLayout->addLayout(caseIdLayout);

        contentLayout = new QHBoxLayout();
        contentLayout->setObjectName("contentLayout");
        stepsGroupBox = new QGroupBox(caseInfoGroupBox);
        stepsGroupBox->setObjectName("stepsGroupBox");
        stepsLayout = new QVBoxLayout(stepsGroupBox);
        stepsLayout->setObjectName("stepsLayout");
        textBrowser = new QTextBrowser(stepsGroupBox);
        textBrowser->setObjectName("textBrowser");
        textBrowser->setMinimumSize(QSize(0, 200));

        stepsLayout->addWidget(textBrowser);


        contentLayout->addWidget(stepsGroupBox);

        expectedGroupBox = new QGroupBox(caseInfoGroupBox);
        expectedGroupBox->setObjectName("expectedGroupBox");
        expectedLayout = new QVBoxLayout(expectedGroupBox);
        expectedLayout->setObjectName("expectedLayout");
        textBrowser_2 = new QTextBrowser(expectedGroupBox);
        textBrowser_2->setObjectName("textBrowser_2");
        textBrowser_2->setMinimumSize(QSize(0, 200));

        expectedLayout->addWidget(textBrowser_2);


        contentLayout->addWidget(expectedGroupBox);


        caseInfoLayout->addLayout(contentLayout);


        mainLayout->addWidget(caseInfoGroupBox);

        controlGroupBox = new QGroupBox(centralwidget);
        controlGroupBox->setObjectName("controlGroupBox");
        controlLayout = new QHBoxLayout(controlGroupBox);
        controlLayout->setObjectName("controlLayout");
        pushButton = new QPushButton(controlGroupBox);
        pushButton->setObjectName("pushButton");
        pushButton->setMinimumSize(QSize(100, 40));

        controlLayout->addWidget(pushButton);

        pushButton_3 = new QPushButton(controlGroupBox);
        pushButton_3->setObjectName("pushButton_3");
        pushButton_3->setMinimumSize(QSize(100, 40));

        controlLayout->addWidget(pushButton_3);

        horizontalSpacer_3 = new QSpacerItem(40, 20, QSizePolicy::Policy::Expanding, QSizePolicy::Policy::Minimum);

        controlLayout->addItem(horizontalSpacer_3);

        pushButton_4 = new QPushButton(controlGroupBox);
        pushButton_4->setObjectName("pushButton_4");
        pushButton_4->setMinimumSize(QSize(100, 40));

        controlLayout->addWidget(pushButton_4);

        pushButton_6 = new QPushButton(controlGroupBox);
        pushButton_6->setObjectName("pushButton_6");
        pushButton_6->setMinimumSize(QSize(100, 40));

        controlLayout->addWidget(pushButton_6);


        mainLayout->addWidget(controlGroupBox);

        MainWindow->setCentralWidget(centralwidget);
        menubar = new QMenuBar(MainWindow);
        menubar->setObjectName("menubar");
        menubar->setGeometry(QRect(0, 0, 800, 17));
        MainWindow->setMenuBar(menubar);
        statusbar = new QStatusBar(MainWindow);
        statusbar->setObjectName("statusbar");
        MainWindow->setStatusBar(statusbar);

        retranslateUi(MainWindow);

        QMetaObject::connectSlotsByName(MainWindow);
    } // setupUi

    void retranslateUi(QMainWindow *MainWindow)
    {
        MainWindow->setWindowTitle(QCoreApplication::translate("MainWindow", "\346\265\213\350\257\225\347\224\250\344\276\213\346\237\245\347\234\213\345\231\250", nullptr));
        fileGroupBox->setTitle(QCoreApplication::translate("MainWindow", "\346\226\207\344\273\266\344\277\241\346\201\257", nullptr));
        pushButton_5->setText(QCoreApplication::translate("MainWindow", "\346\211\223\345\274\200Excel\346\226\207\344\273\266", nullptr));
        label_4->setText(QCoreApplication::translate("MainWindow", "\345\275\223\345\211\215\346\226\207\344\273\266\357\274\232", nullptr));
        label->setText(QCoreApplication::translate("MainWindow", "\346\234\252\351\200\211\346\213\251\346\226\207\344\273\266", nullptr));
        label->setStyleSheet(QCoreApplication::translate("MainWindow", "color: #666;", nullptr));
        caseInfoGroupBox->setTitle(QCoreApplication::translate("MainWindow", "\346\265\213\350\257\225\347\224\250\344\276\213\344\277\241\346\201\257", nullptr));
        label_6->setText(QCoreApplication::translate("MainWindow", "\347\224\250\344\276\213ID\357\274\232", nullptr));
        label_5->setText(QCoreApplication::translate("MainWindow", "-", nullptr));
        label_5->setStyleSheet(QCoreApplication::translate("MainWindow", "font-weight: bold; color: #2c3e50;", nullptr));
        pushButton_2->setText(QCoreApplication::translate("MainWindow", "\345\244\215\345\210\266ID", nullptr));
        stepsGroupBox->setTitle(QCoreApplication::translate("MainWindow", "\346\265\213\350\257\225\346\255\245\351\252\244", nullptr));
        expectedGroupBox->setTitle(QCoreApplication::translate("MainWindow", "\351\242\204\346\234\237\347\273\223\346\236\234", nullptr));
        controlGroupBox->setTitle(QCoreApplication::translate("MainWindow", "\346\223\215\344\275\234\346\216\247\345\210\266", nullptr));
        pushButton->setText(QCoreApplication::translate("MainWindow", "\342\206\220 \344\270\212\344\270\200\344\270\252", nullptr));
        pushButton_3->setText(QCoreApplication::translate("MainWindow", "\344\270\213\344\270\200\344\270\252 \342\206\222", nullptr));
        pushButton_4->setText(QCoreApplication::translate("MainWindow", "\342\235\214 \345\244\261\350\264\245", nullptr));
        pushButton_4->setStyleSheet(QCoreApplication::translate("MainWindow", "QPushButton { background-color: #f8f9fa; border: 2px solid #dc3545; color: #dc3545; border-radius: 5px; }\n"
"QPushButton:hover { background-color: #dc3545; color: white; }", nullptr));
        pushButton_6->setText(QCoreApplication::translate("MainWindow", "\342\234\205 \351\200\232\350\277\207", nullptr));
        pushButton_6->setStyleSheet(QCoreApplication::translate("MainWindow", "QPushButton { background-color: #f8f9fa; border: 2px solid #28a745; color: #28a745; border-radius: 5px; }\n"
"QPushButton:hover { background-color: #28a745; color: white; }", nullptr));
    } // retranslateUi

};

namespace Ui {
    class MainWindow: public Ui_MainWindow {};
} // namespace Ui

QT_END_NAMESPACE

#endif // UI_MAINWINDOW_H
