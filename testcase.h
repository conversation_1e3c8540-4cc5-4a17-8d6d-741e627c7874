#ifndef TESTCASE_H
#define TESTCASE_H

#include <QString>
#include <QList>

/**
 * @brief 测试用例数据结构
 * 
 * 存储从Excel文件中读取的测试用例信息
 */
class TestCase
{
public:
    enum TestResult {
        NotTested = 0,  // 未测试（空白）
        Passed = 1,     // 通过
        Failed = 2      // 失败
    };

    TestCase();
    TestCase(const QString &caseId, const QString &testSteps, const QString &expectedResult, TestResult result = NotTested);
    
    // Getter方法
    QString getCaseId() const { return m_caseId; }
    QString getTestSteps() const { return m_testSteps; }
    QString getExpectedResult() const { return m_expectedResult; }
    TestResult getTestResult() const { return m_testResult; }
    int getRowNumber() const { return m_rowNumber; }
    
    // Setter方法
    void setCaseId(const QString &caseId) { m_caseId = caseId; }
    void setTestSteps(const QString &testSteps) { m_testSteps = testSteps; }
    void setExpectedResult(const QString &expectedResult) { m_expectedResult = expectedResult; }
    void setTestResult(TestResult result) { m_testResult = result; }
    void setRowNumber(int rowNumber) { m_rowNumber = rowNumber; }
    
    // 工具方法
    QString getTestResultString() const;
    static TestResult parseTestResult(const QString &resultStr);
    bool isEmpty() const;

private:
    QString m_caseId;           // A列：用例编号
    QString m_testSteps;        // U列：测试步骤
    QString m_expectedResult;   // V列：预期结果
    TestResult m_testResult;    // AH列：测试结果
    int m_rowNumber;            // Excel中的行号（用于写回数据）
};

/**
 * @brief 测试用例管理器
 * 
 * 管理所有测试用例，提供导航和操作功能
 */
class TestCaseManager
{
public:
    TestCaseManager();
    
    // 测试用例管理
    void addTestCase(const TestCase &testCase);
    void clearTestCases();
    int getTestCaseCount() const { return m_testCases.size(); }
    bool isEmpty() const { return m_testCases.isEmpty(); }
    
    // 当前测试用例操作
    TestCase* getCurrentTestCase();
    const TestCase* getCurrentTestCase() const;
    int getCurrentIndex() const { return m_currentIndex; }
    void setCurrentIndex(int index);
    
    // 导航功能
    bool hasNext() const;
    bool hasPrevious() const;
    bool moveToNext();
    bool moveToPrevious();
    void moveToFirst();
    void moveToLast();
    
    // 测试结果操作
    void setCurrentTestResult(TestCase::TestResult result);
    
    // 获取所有测试用例（用于保存）
    const QList<TestCase>& getAllTestCases() const { return m_testCases; }

private:
    QList<TestCase> m_testCases;
    int m_currentIndex;
};

#endif // TESTCASE_H
