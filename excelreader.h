#ifndef EXCELREADER_H
#define EXCELREADER_H

#include <QString>
#include <QList>
#include "testcase.h"
#include <xlnt/xlnt.hpp>

/**
 * @brief Excel文件读取器
 * 
 * 负责读取和写入Excel文件中的测试用例数据
 */
class ExcelReader
{
public:
    ExcelReader();
    ~ExcelReader();
    
    /**
     * @brief 读取Excel文件
     * @param filePath Excel文件路径
     * @return 是否读取成功
     */
    bool readExcelFile(const QString &filePath);
    
    /**
     * @brief 保存Excel文件
     * @param filePath Excel文件路径（如果为空则保存到原文件）
     * @return 是否保存成功
     */
    bool saveExcelFile(const QString &filePath = QString());
    
    /**
     * @brief 获取读取的测试用例列表
     * @return 测试用例列表
     */
    QList<TestCase> getTestCases() const { return m_testCases; }
    
    /**
     * @brief 更新测试用例结果
     * @param testCases 更新后的测试用例列表
     */
    void updateTestCases(const QList<TestCase> &testCases);
    
    /**
     * @brief 获取最后的错误信息
     * @return 错误信息
     */
    QString getLastError() const { return m_lastError; }
    
    /**
     * @brief 检查文件是否已加载
     * @return 是否已加载文件
     */
    bool isFileLoaded() const { return m_isLoaded; }

private:
    /**
     * @brief 解析Excel行数据
     * @param row 行号（从1开始）
     * @return 解析的测试用例，如果行为空则返回空的TestCase
     */
    TestCase parseRow(int row);
    
    /**
     * @brief 检查行是否为空
     * @param row 行号
     * @return 是否为空行
     */
    bool isRowEmpty(int row);
    
    /**
     * @brief 获取单元格值
     * @param row 行号
     * @param col 列号
     * @return 单元格值
     */
    QString getCellValue(int row, int col);
    
    /**
     * @brief 设置单元格值
     * @param row 行号
     * @param col 列号
     * @param value 值
     */
    void setCellValue(int row, int col, const QString &value);
    
    /**
     * @brief 列字母转换为列号
     * @param columnLetter 列字母（如"A", "AH"）
     * @return 列号（从1开始）
     */
    static int columnLetterToNumber(const QString &columnLetter);

private:
    xlnt::workbook m_workbook;
    xlnt::worksheet m_worksheet;
    QList<TestCase> m_testCases;
    QString m_filePath;
    QString m_lastError;
    bool m_isLoaded;
    
    // Excel列定义
    static const int CASE_ID_COLUMN = 1;        // A列：用例编号
    static const int TEST_STEPS_COLUMN = 21;    // U列：测试步骤
    static const int EXPECTED_RESULT_COLUMN = 22; // V列：预期结果
    static const int TEST_RESULT_COLUMN = 34;   // AH列：测试结果
};

#endif // EXCELREADER_H
