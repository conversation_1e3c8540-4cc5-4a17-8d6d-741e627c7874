#include "testcase.h"

// TestCase 实现
TestCase::TestCase()
    : m_testResult(NotTested), m_rowNumber(-1)
{
}

TestCase::TestCase(const QString &caseId, const QString &testSteps, const QString &expectedResult, TestResult result)
    : m_caseId(caseId), m_testSteps(testSteps), m_expectedResult(expectedResult), m_testResult(result), m_rowNumber(-1)
{
}

QString TestCase::getTestResultString() const
{
    switch (m_testResult) {
        case Passed:
            return "1";
        case Failed:
            return "0";
        case NotTested:
        default:
            return "";
    }
}

TestCase::TestResult TestCase::parseTestResult(const QString &resultStr)
{
    if (resultStr.trimmed() == "1") {
        return Passed;
    } else if (resultStr.trimmed() == "0") {
        return Failed;
    } else {
        return NotTested;
    }
}

bool TestCase::isEmpty() const
{
    return m_caseId.isEmpty() && m_testSteps.isEmpty() && m_expectedResult.isEmpty();
}

// TestCaseManager 实现
TestCaseManager::TestCaseManager()
    : m_currentIndex(-1)
{
}

void TestCaseManager::addTestCase(const TestCase &testCase)
{
    if (!testCase.isEmpty()) {
        m_testCases.append(testCase);
        if (m_currentIndex == -1) {
            m_currentIndex = 0;
        }
    }
}

void TestCaseManager::clearTestCases()
{
    m_testCases.clear();
    m_currentIndex = -1;
}

TestCase* TestCaseManager::getCurrentTestCase()
{
    if (m_currentIndex >= 0 && m_currentIndex < m_testCases.size()) {
        return &m_testCases[m_currentIndex];
    }
    return nullptr;
}

const TestCase* TestCaseManager::getCurrentTestCase() const
{
    if (m_currentIndex >= 0 && m_currentIndex < m_testCases.size()) {
        return &m_testCases[m_currentIndex];
    }
    return nullptr;
}

void TestCaseManager::setCurrentIndex(int index)
{
    if (index >= 0 && index < m_testCases.size()) {
        m_currentIndex = index;
    }
}

bool TestCaseManager::hasNext() const
{
    return m_currentIndex >= 0 && m_currentIndex < m_testCases.size() - 1;
}

bool TestCaseManager::hasPrevious() const
{
    return m_currentIndex > 0;
}

bool TestCaseManager::moveToNext()
{
    if (hasNext()) {
        m_currentIndex++;
        return true;
    }
    return false;
}

bool TestCaseManager::moveToPrevious()
{
    if (hasPrevious()) {
        m_currentIndex--;
        return true;
    }
    return false;
}

void TestCaseManager::moveToFirst()
{
    if (!m_testCases.isEmpty()) {
        m_currentIndex = 0;
    }
}

void TestCaseManager::moveToLast()
{
    if (!m_testCases.isEmpty()) {
        m_currentIndex = m_testCases.size() - 1;
    }
}

void TestCaseManager::setCurrentTestResult(TestCase::TestResult result)
{
    TestCase* currentCase = getCurrentTestCase();
    if (currentCase) {
        currentCase->setTestResult(result);
    }
}
