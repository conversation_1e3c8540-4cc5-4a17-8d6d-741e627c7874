cmake_minimum_required(VERSION 3.16)

project(testcase-viewer VERSION 1.0.0 LANGUAGES CXX)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# 设置vcpkg工具链
include("C:/vcpkg/scripts/buildsystems/vcpkg.cmake")

# 查找Qt6包
find_package(Qt6 REQUIRED COMPONENTS Core Widgets)

# 查找Excel读取库 (使用xlnt)
find_package(xlnt REQUIRED)

# 启用Qt的MOC、UIC、RCC
set(CMAKE_AUTOMOC ON)
set(CMAKE_AUTOUIC ON)
set(CMAKE_AUTORCC ON)

# 源文件
set(SOURCES
    main.cpp
    mainwindow.cpp
    testcase.cpp
    excelreader.cpp
)

# 头文件
set(HEADERS
    mainwindow.h
    testcase.h
    excelreader.h
)

# UI文件
set(UI_FILES
    mainwindow.ui
)

# 创建可执行文件
add_executable(testcase-viewer
    ${SOURCES}
    ${HEADERS}
    ${UI_FILES}
)

# 链接Qt库
target_link_libraries(testcase-viewer
    Qt6::Core
    Qt6::Widgets
    xlnt::xlnt
)

# 设置输出目录
set_target_properties(testcase-viewer PROPERTIES
    RUNTIME_OUTPUT_DIRECTORY ${CMAKE_BINARY_DIR}/bin
)

# Windows特定设置
if(WIN32)
    set_target_properties(testcase-viewer PROPERTIES
        WIN32_EXECUTABLE TRUE
    )
endif()
