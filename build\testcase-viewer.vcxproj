﻿<?xml version="1.0" encoding="utf-8"?>
<Project DefaultTargets="Build" ToolsVersion="17.0" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <PreferredToolArchitecture>x64</PreferredToolArchitecture>
  </PropertyGroup>
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="MinSizeRel|x64">
      <Configuration>MinSizeRel</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="RelWithDebInfo|x64">
      <Configuration>RelWithDebInfo</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Globals">
    <ProjectGuid>{48EA70E9-7D21-33C9-AACA-58FEC9CD0E21}</ProjectGuid>
    <Keyword>Win32Proj</Keyword>
    <VcpkgEnabled>false</VcpkgEnabled>
    <WindowsTargetPlatformVersion>10.0.26100.0</WindowsTargetPlatformVersion>
    <Platform>x64</Platform>
    <ProjectName>testcase-viewer</ProjectName>
    <VCProjectUpgraderObjectName>NoUpgrade</VCProjectUpgraderObjectName>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'" Label="Configuration">
    <ConfigurationType>Application</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="do_not_import_user.props" Condition="exists('do_not_import_user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <PropertyGroup>
    <_ProjectFileVersion>10.0.20506.1</_ProjectFileVersion>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Documents\testcase-viewer\build\bin\Debug\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">testcase-viewer.dir\Debug\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">testcase-viewer</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Documents\testcase-viewer\build\bin\Release\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='Release|x64'">testcase-viewer.dir\Release\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='Release|x64'">testcase-viewer</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='Release|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Documents\testcase-viewer\build\bin\MinSizeRel\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">testcase-viewer.dir\MinSizeRel\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">testcase-viewer</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</GenerateManifest>
    <OutDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Documents\testcase-viewer\build\bin\RelWithDebInfo\</OutDir>
    <IntDir Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">testcase-viewer.dir\RelWithDebInfo\</IntDir>
    <TargetName Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">testcase-viewer</TargetName>
    <TargetExt Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">.exe</TargetExt>
    <LinkIncremental Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</LinkIncremental>
    <GenerateManifest Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</GenerateManifest>
  </PropertyGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_Debug;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/vcpkg/installed/x64-windows/include/Qt6/QtCore" /external:I "C:/vcpkg/installed/x64-windows/include/Qt6" /external:I "C:/vcpkg/installed/x64-windows/share/Qt6/mkspecs/win32-msvc" /external:I "C:/vcpkg/installed/x64-windows/include/Qt6/QtWidgets" /external:I "C:/vcpkg/installed/x64-windows/include/Qt6/QtGui" /external:I "C:/vcpkg/installed/x64-windows/include" -Zc:__cplusplus -utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <BasicRuntimeChecks>EnableFastChecks</BasicRuntimeChecks>
      <ConformanceMode>true</ConformanceMode>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>Disabled</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>Disabled</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDebugDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;QT_CORE_LIB;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;CMAKE_INTDIR="Debug"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_DEBUG;_WINDOWS;QT_CORE_LIB;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;CMAKE_INTDIR=\"Debug\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_Debug;C:\vcpkg\installed\x64-windows\include\Qt6\QtCore;C:\vcpkg\installed\x64-windows\include\Qt6;C:\vcpkg\installed\x64-windows\share\Qt6\mkspecs\win32-msvc;C:\vcpkg\installed\x64-windows\include\Qt6\QtWidgets;C:\vcpkg\installed\x64-windows\include\Qt6\QtGui;C:\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_Debug;C:\vcpkg\installed\x64-windows\include\Qt6\QtCore;C:\vcpkg\installed\x64-windows\include\Qt6;C:\vcpkg\installed\x64-windows\share\Qt6\mkspecs\win32-msvc;C:\vcpkg\installed\x64-windows\include\Qt6\QtWidgets;C:\vcpkg\installed\x64-windows\include\Qt6\QtGui;C:\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target testcase-viewer</Message>
      <Command>setlocal
cd C:\Users\<USER>\Documents\testcase-viewer\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E cmake_autogen C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/testcase-viewer_autogen.dir/AutogenInfo.json Debug
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Documents/testcase-viewer/build/testcase-viewer_autogen/autouic_Debug.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file C:/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary C:/Users/<USER>/Documents/testcase-viewer/build/bin/Debug/testcase-viewer.exe -installedDir C:/vcpkg/installed/x64-windows/debug/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\vcpkg\installed\x64-windows\debug\lib\Qt6Widgetsd.lib;C:\vcpkg\installed\x64-windows\debug\lib\xlntd.lib;C:\vcpkg\installed\x64-windows\debug\lib\Qt6Guid.lib;C:\vcpkg\installed\x64-windows\debug\lib\Qt6Cored.lib;mpr.lib;userenv.lib;C:\vcpkg\installed\x64-windows\debug\lib\Qt6EntryPointd.lib;shell32.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Documents/testcase-viewer/build/Debug/testcase-viewer.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Documents/testcase-viewer/build/bin/Debug/testcase-viewer.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='Release|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_Release;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/vcpkg/installed/x64-windows/include/Qt6/QtCore" /external:I "C:/vcpkg/installed/x64-windows/include/Qt6" /external:I "C:/vcpkg/installed/x64-windows/share/Qt6/mkspecs/win32-msvc" /external:I "C:/vcpkg/installed/x64-windows/include/Qt6/QtWidgets" /external:I "C:/vcpkg/installed/x64-windows/include/Qt6/QtGui" /external:I "C:/vcpkg/installed/x64-windows/include" -Zc:__cplusplus -utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ConformanceMode>true</ConformanceMode>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>AnySuitable</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;CMAKE_INTDIR="Release"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;CMAKE_INTDIR=\"Release\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_Release;C:\vcpkg\installed\x64-windows\include\Qt6\QtCore;C:\vcpkg\installed\x64-windows\include\Qt6;C:\vcpkg\installed\x64-windows\share\Qt6\mkspecs\win32-msvc;C:\vcpkg\installed\x64-windows\include\Qt6\QtWidgets;C:\vcpkg\installed\x64-windows\include\Qt6\QtGui;C:\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_Release;C:\vcpkg\installed\x64-windows\include\Qt6\QtCore;C:\vcpkg\installed\x64-windows\include\Qt6;C:\vcpkg\installed\x64-windows\share\Qt6\mkspecs\win32-msvc;C:\vcpkg\installed\x64-windows\include\Qt6\QtWidgets;C:\vcpkg\installed\x64-windows\include\Qt6\QtGui;C:\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target testcase-viewer</Message>
      <Command>setlocal
cd C:\Users\<USER>\Documents\testcase-viewer\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E cmake_autogen C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/testcase-viewer_autogen.dir/AutogenInfo.json Release
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Documents/testcase-viewer/build/testcase-viewer_autogen/autouic_Release.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file C:/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary C:/Users/<USER>/Documents/testcase-viewer/build/bin/Release/testcase-viewer.exe -installedDir C:/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\vcpkg\installed\x64-windows\lib\Qt6Widgets.lib;C:\vcpkg\installed\x64-windows\lib\xlnt.lib;C:\vcpkg\installed\x64-windows\lib\Qt6Gui.lib;C:\vcpkg\installed\x64-windows\lib\Qt6Core.lib;mpr.lib;userenv.lib;C:\vcpkg\installed\x64-windows\lib\Qt6EntryPoint.lib;shell32.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Documents/testcase-viewer/build/Release/testcase-viewer.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Documents/testcase-viewer/build/bin/Release/testcase-viewer.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_MinSizeRel;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/vcpkg/installed/x64-windows/include/Qt6/QtCore" /external:I "C:/vcpkg/installed/x64-windows/include/Qt6" /external:I "C:/vcpkg/installed/x64-windows/share/Qt6/mkspecs/win32-msvc" /external:I "C:/vcpkg/installed/x64-windows/include/Qt6/QtWidgets" /external:I "C:/vcpkg/installed/x64-windows/include/Qt6/QtGui" /external:I "C:/vcpkg/installed/x64-windows/include" -Zc:__cplusplus -utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ConformanceMode>true</ConformanceMode>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MinSpace</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;CMAKE_INTDIR="MinSizeRel"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <DebugInformationFormat>
      </DebugInformationFormat>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;CMAKE_INTDIR=\"MinSizeRel\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_MinSizeRel;C:\vcpkg\installed\x64-windows\include\Qt6\QtCore;C:\vcpkg\installed\x64-windows\include\Qt6;C:\vcpkg\installed\x64-windows\share\Qt6\mkspecs\win32-msvc;C:\vcpkg\installed\x64-windows\include\Qt6\QtWidgets;C:\vcpkg\installed\x64-windows\include\Qt6\QtGui;C:\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_MinSizeRel;C:\vcpkg\installed\x64-windows\include\Qt6\QtCore;C:\vcpkg\installed\x64-windows\include\Qt6;C:\vcpkg\installed\x64-windows\share\Qt6\mkspecs\win32-msvc;C:\vcpkg\installed\x64-windows\include\Qt6\QtWidgets;C:\vcpkg\installed\x64-windows\include\Qt6\QtGui;C:\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target testcase-viewer</Message>
      <Command>setlocal
cd C:\Users\<USER>\Documents\testcase-viewer\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E cmake_autogen C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/testcase-viewer_autogen.dir/AutogenInfo.json MinSizeRel
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Documents/testcase-viewer/build/testcase-viewer_autogen/autouic_MinSizeRel.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file C:/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary C:/Users/<USER>/Documents/testcase-viewer/build/bin/MinSizeRel/testcase-viewer.exe -installedDir C:/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\vcpkg\installed\x64-windows\lib\Qt6Widgets.lib;C:\vcpkg\installed\x64-windows\lib\xlnt.lib;C:\vcpkg\installed\x64-windows\lib\Qt6Gui.lib;C:\vcpkg\installed\x64-windows\lib\Qt6Core.lib;mpr.lib;userenv.lib;C:\vcpkg\installed\x64-windows\lib\Qt6EntryPoint.lib;shell32.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>false</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Documents/testcase-viewer/build/MinSizeRel/testcase-viewer.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Documents/testcase-viewer/build/bin/MinSizeRel/testcase-viewer.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">
    <ClCompile>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_RelWithDebInfo;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <AdditionalOptions>%(AdditionalOptions) /external:I "C:/vcpkg/installed/x64-windows/include/Qt6/QtCore" /external:I "C:/vcpkg/installed/x64-windows/include/Qt6" /external:I "C:/vcpkg/installed/x64-windows/share/Qt6/mkspecs/win32-msvc" /external:I "C:/vcpkg/installed/x64-windows/include/Qt6/QtWidgets" /external:I "C:/vcpkg/installed/x64-windows/include/Qt6/QtGui" /external:I "C:/vcpkg/installed/x64-windows/include" -Zc:__cplusplus -utf-8</AdditionalOptions>
      <AssemblerListingLocation>$(IntDir)</AssemblerListingLocation>
      <ConformanceMode>true</ConformanceMode>
      <DebugInformationFormat>ProgramDatabase</DebugInformationFormat>
      <ExceptionHandling>Sync</ExceptionHandling>
      <ExternalWarningLevel>TurnOffAllWarnings</ExternalWarningLevel>
      <InlineFunctionExpansion>OnlyExplicitInline</InlineFunctionExpansion>
      <LanguageStandard>stdcpp17</LanguageStandard>
      <Optimization>MaxSpeed</Optimization>
      <PrecompiledHeader>NotUsing</PrecompiledHeader>
      <RuntimeLibrary>MultiThreadedDLL</RuntimeLibrary>
      <RuntimeTypeInfo>true</RuntimeTypeInfo>
      <UseFullPaths>false</UseFullPaths>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;CMAKE_INTDIR="RelWithDebInfo"</PreprocessorDefinitions>
      <ObjectFileName>$(IntDir)</ObjectFileName>
      <ScanSourceForModuleDependencies>false</ScanSourceForModuleDependencies>
    </ClCompile>
    <ResourceCompile>
      <PreprocessorDefinitions>%(PreprocessorDefinitions);WIN32;_WINDOWS;NDEBUG;QT_CORE_LIB;QT_NO_DEBUG;_ENABLE_EXTENDED_ALIGNED_STORAGE;WIN64;_WIN64;UNICODE;_UNICODE;QT_WIDGETS_LIB;QT_GUI_LIB;CMAKE_INTDIR=\"RelWithDebInfo\"</PreprocessorDefinitions>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_RelWithDebInfo;C:\vcpkg\installed\x64-windows\include\Qt6\QtCore;C:\vcpkg\installed\x64-windows\include\Qt6;C:\vcpkg\installed\x64-windows\share\Qt6\mkspecs\win32-msvc;C:\vcpkg\installed\x64-windows\include\Qt6\QtWidgets;C:\vcpkg\installed\x64-windows\include\Qt6\QtGui;C:\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
    </ResourceCompile>
    <Midl>
      <AdditionalIncludeDirectories>C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_RelWithDebInfo;C:\vcpkg\installed\x64-windows\include\Qt6\QtCore;C:\vcpkg\installed\x64-windows\include\Qt6;C:\vcpkg\installed\x64-windows\share\Qt6\mkspecs\win32-msvc;C:\vcpkg\installed\x64-windows\include\Qt6\QtWidgets;C:\vcpkg\installed\x64-windows\include\Qt6\QtGui;C:\vcpkg\installed\x64-windows\include;%(AdditionalIncludeDirectories)</AdditionalIncludeDirectories>
      <OutputDirectory>$(ProjectDir)/$(IntDir)</OutputDirectory>
      <HeaderFileName>%(Filename).h</HeaderFileName>
      <TypeLibraryName>%(Filename).tlb</TypeLibraryName>
      <InterfaceIdentifierFileName>%(Filename)_i.c</InterfaceIdentifierFileName>
      <ProxyFileName>%(Filename)_p.c</ProxyFileName>
    </Midl>
    <PreBuildEvent>
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message>Automatic MOC and UIC for target testcase-viewer</Message>
      <Command>setlocal
cd C:\Users\<USER>\Documents\testcase-viewer\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E cmake_autogen C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/testcase-viewer_autogen.dir/AutogenInfo.json RelWithDebInfo
if %errorlevel% neq 0 goto :cmEnd
"C:\Program Files\CMake\bin\cmake.exe" -E touch C:/Users/<USER>/Documents/testcase-viewer/build/testcase-viewer_autogen/autouic_RelWithDebInfo.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PreBuildEvent>
    <PostBuildEvent>
      <Message></Message>
      <Command>setlocal
C:\Windows\System32\WindowsPowerShell\v1.0\powershell.exe -noprofile -executionpolicy Bypass -file C:/vcpkg/scripts/buildsystems/msbuild/applocal.ps1 -targetBinary C:/Users/<USER>/Documents/testcase-viewer/build/bin/RelWithDebInfo/testcase-viewer.exe -installedDir C:/vcpkg/installed/x64-windows/bin -OutVariable out
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
    </PostBuildEvent>
    <Link>
      <AdditionalDependencies>C:\vcpkg\installed\x64-windows\lib\Qt6Widgets.lib;C:\vcpkg\installed\x64-windows\lib\xlnt.lib;C:\vcpkg\installed\x64-windows\lib\Qt6Gui.lib;C:\vcpkg\installed\x64-windows\lib\Qt6Core.lib;mpr.lib;userenv.lib;C:\vcpkg\installed\x64-windows\lib\Qt6EntryPoint.lib;shell32.lib;d3d11.lib;dxgi.lib;dxguid.lib;d3d12.lib;kernel32.lib;user32.lib;gdi32.lib;winspool.lib;shell32.lib;ole32.lib;oleaut32.lib;uuid.lib;comdlg32.lib;advapi32.lib</AdditionalDependencies>
      <AdditionalLibraryDirectories>%(AdditionalLibraryDirectories)</AdditionalLibraryDirectories>
      <AdditionalOptions>%(AdditionalOptions) /machine:x64</AdditionalOptions>
      <GenerateDebugInformation>true</GenerateDebugInformation>
      <IgnoreSpecificDefaultLibraries>%(IgnoreSpecificDefaultLibraries)</IgnoreSpecificDefaultLibraries>
      <ImportLibrary>C:/Users/<USER>/Documents/testcase-viewer/build/RelWithDebInfo/testcase-viewer.lib</ImportLibrary>
      <ProgramDataBaseFile>C:/Users/<USER>/Documents/testcase-viewer/build/bin/RelWithDebInfo/testcase-viewer.pdb</ProgramDataBaseFile>
      <SubSystem>Windows</SubSystem>
    </Link>
    <ProjectReference>
      <LinkLibraryDependencies>false</LinkLibraryDependencies>
    </ProjectReference>
  </ItemDefinitionGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\34b19745bca69bd124befcdbb60d74e3\autouic_(CONFIG).stamp.rule">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
cd C:\Users\<USER>\Documents\testcase-viewer\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Documents\testcase-viewer\mainwindow.ui;C:\vcpkg\installed\x64-windows\tools\Qt6\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\autouic_Debug.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
cd C:\Users\<USER>\Documents\testcase-viewer\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Documents\testcase-viewer\mainwindow.ui;C:\vcpkg\installed\x64-windows\tools\Qt6\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\autouic_Release.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
cd C:\Users\<USER>\Documents\testcase-viewer\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Documents\testcase-viewer\mainwindow.ui;C:\vcpkg\installed\x64-windows\tools\Qt6\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\autouic_MinSizeRel.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <BuildInParallel Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</BuildInParallel>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'"></Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
cd C:\Users\<USER>\Documents\testcase-viewer\build
if %errorlevel% neq 0 goto :cmEnd
C:
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Documents\testcase-viewer\mainwindow.ui;C:\vcpkg\installed\x64-windows\tools\Qt6\bin\uic.exe;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\autouic_RelWithDebInfo.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <CustomBuild Include="C:\Users\<USER>\Documents\testcase-viewer\CMakeLists.txt">
      <UseUtf8Encoding>Always</UseUtf8Encoding>
      <Message Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">Building Custom Rule C:/Users/<USER>/Documents/testcase-viewer/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Documents/testcase-viewer -BC:/Users/<USER>/Documents/testcase-viewer/build --check-stamp-file C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeCXXCompiler.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeRCCompiler.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeSystem.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\FastFloatConfig.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\FastFloatConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\fast_float-targets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\FindWrapAtomic.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Config.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigExtras.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Dependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Targets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6VersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtFeature.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtFeatureCommon.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtInstallPaths.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicAppleHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicCMakeHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicDependencyHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicExternalProjectHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicFinalizerHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicFindPackageHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicGitHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicPluginHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomCpeHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomDepHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomFileHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomOpsHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomPurlHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomPythonHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicTargetHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicTestHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicToolHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicWalkLibsHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigExtras.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreMacros.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiPlugins.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppConfig.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppTargets.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntConfig.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets.cmake;C:\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='Release|x64'">Building Custom Rule C:/Users/<USER>/Documents/testcase-viewer/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='Release|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Documents/testcase-viewer -BC:/Users/<USER>/Documents/testcase-viewer/build --check-stamp-file C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeCXXCompiler.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeRCCompiler.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeSystem.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\FastFloatConfig.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\FastFloatConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\fast_float-targets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\FindWrapAtomic.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Config.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigExtras.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Dependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Targets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6VersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtFeature.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtFeatureCommon.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtInstallPaths.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicAppleHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicCMakeHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicDependencyHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicExternalProjectHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicFinalizerHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicFindPackageHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicGitHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicPluginHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomCpeHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomDepHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomFileHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomOpsHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomPurlHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomPythonHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicTargetHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicTestHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicToolHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicWalkLibsHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigExtras.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreMacros.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiPlugins.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppConfig.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppTargets.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntConfig.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets.cmake;C:\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='Release|x64'">C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='Release|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">Building Custom Rule C:/Users/<USER>/Documents/testcase-viewer/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Documents/testcase-viewer -BC:/Users/<USER>/Documents/testcase-viewer/build --check-stamp-file C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeCXXCompiler.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeRCCompiler.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeSystem.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\FastFloatConfig.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\FastFloatConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\fast_float-targets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\FindWrapAtomic.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Config.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigExtras.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Dependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Targets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6VersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtFeature.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtFeatureCommon.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtInstallPaths.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicAppleHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicCMakeHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicDependencyHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicExternalProjectHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicFinalizerHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicFindPackageHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicGitHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicPluginHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomCpeHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomDepHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomFileHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomOpsHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomPurlHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomPythonHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicTargetHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicTestHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicToolHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicWalkLibsHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigExtras.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreMacros.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiPlugins.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppConfig.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppTargets.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntConfig.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets.cmake;C:\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">false</LinkObjects>
      <Message Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">Building Custom Rule C:/Users/<USER>/Documents/testcase-viewer/CMakeLists.txt</Message>
      <Command Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">setlocal
"C:\Program Files\CMake\bin\cmake.exe" -SC:/Users/<USER>/Documents/testcase-viewer -BC:/Users/<USER>/Documents/testcase-viewer/build --check-stamp-file C:/Users/<USER>/Documents/testcase-viewer/build/CMakeFiles/generate.stamp
if %errorlevel% neq 0 goto :cmEnd
:cmEnd
endlocal &amp; call :cmErrorLevel %errorlevel% &amp; goto :cmDone
:cmErrorLevel
exit /b %1
:cmDone
if %errorlevel% neq 0 goto :VCEnd</Command>
      <AdditionalInputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCXXInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCheckCompilerFlagCommonPatterns.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeCommonLanguageInclude.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeDependentOption.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeFindDependencyMacro.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeGenericSystem.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeInitializeConfigs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeLanguageInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeRCInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeSystemSpecificInformation.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CMakeSystemSpecificInitialize.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckCXXSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckIncludeFileCXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\CheckLibraryExists.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\CMakeCommonCompilerMacros.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Compiler\MSVC.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageHandleStandardArgs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindPackageMessage.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\FindThreads.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\GNUInstallDirs.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckCompilerFlag.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckFlagCommonConfig.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Internal\CheckSourceCompiles.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-Initialize.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-MSVC-CXX.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows-MSVC.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\Windows.cmake;C:\Program Files\CMake\share\cmake-3.28\Modules\Platform\WindowsPaths.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeCXXCompiler.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeRCCompiler.cmake;C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\3.28.6\CMakeSystem.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\FastFloatConfig.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\FastFloatConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\FastFloat\fast_float-targets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\FindWrapAtomic.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Config.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigExtras.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6ConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Dependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6Targets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\Qt6VersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtFeature.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtFeatureCommon.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtInstallPaths.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicAppleHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicCMakeHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicCMakeVersionHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicDependencyHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicExternalProjectHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicFinalizerHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicFindPackageHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicGitHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicPluginHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomAttributionHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomCpeHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomDepHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomFileHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomGenerationHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomLicenseHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomOpsHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomPurlHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomPythonHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomQtEntityHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicSbomSystemDepHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicTargetHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicTestHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicToolHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6\QtPublicWalkLibsHelpers.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigExtras.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreMacros.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Core\Qt6CoreVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6CoreTools\Qt6CoreToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6EntryPointPrivate\Qt6EntryPointPrivateVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiPlugins.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6GuiVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QGifPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QICOPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QJpegPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QMinimalIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QOffscreenIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QTuioTouchPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsDirect2DIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Gui\Qt6QWindowsIntegrationPluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6GuiTools\Qt6GuiToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6QModernWindowsStylePluginTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsMacros.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsPlugins.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6Widgets\Qt6WidgetsVersionlessAliasTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsAdditionalTargetInfo.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfig.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsConfigVersionImpl.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsDependencies.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsTargets.cmake;C:\vcpkg\installed\x64-windows\share\Qt6WidgetsTools\Qt6WidgetsToolsVersionlessTargets.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-config-version.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-config.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-debug.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets-release.cmake;C:\vcpkg\installed\x64-windows\share\fmt\fmt-targets.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppConfig.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\utf8cpp\utf8cppTargets.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntConfig.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntConfigVersion.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets-debug.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets-release.cmake;C:\vcpkg\installed\x64-windows\share\xlnt\XlntTargets.cmake;C:\vcpkg\scripts\buildsystems\vcpkg.cmake;%(AdditionalInputs)</AdditionalInputs>
      <Outputs Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">C:\Users\<USER>\Documents\testcase-viewer\build\CMakeFiles\generate.stamp</Outputs>
      <LinkObjects Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">false</LinkObjects>
    </CustomBuild>
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\mocs_compilation_Debug.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClCompile Include="C:\Users\<USER>\Documents\testcase-viewer\main.cpp" />
    <ClCompile Include="C:\Users\<USER>\Documents\testcase-viewer\mainwindow.cpp" />
    <ClCompile Include="C:\Users\<USER>\Documents\testcase-viewer\testcase.cpp" />
    <ClCompile Include="C:\Users\<USER>\Documents\testcase-viewer\excelreader.cpp" />
    <ClInclude Include="C:\Users\<USER>\Documents\testcase-viewer\mainwindow.h" />
    <ClInclude Include="C:\Users\<USER>\Documents\testcase-viewer\testcase.h" />
    <ClInclude Include="C:\Users\<USER>\Documents\testcase-viewer\excelreader.h" />
    <None Include="C:\Users\<USER>\Documents\testcase-viewer\mainwindow.ui">
    </None>
    <ClInclude Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_Debug\ui_mainwindow.h" />
    <None Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\autouic_Debug.stamp">
    </None>
    <ClCompile Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\mocs_compilation_Release.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClInclude Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_Release\ui_mainwindow.h" />
    <None Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\autouic_Release.stamp">
    </None>
    <ClCompile Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\mocs_compilation_MinSizeRel.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='RelWithDebInfo|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClInclude Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_MinSizeRel\ui_mainwindow.h" />
    <None Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\autouic_MinSizeRel.stamp">
    </None>
    <ClCompile Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\mocs_compilation_RelWithDebInfo.cpp">
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Debug|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='Release|x64'">true</ExcludedFromBuild>
      <ExcludedFromBuild Condition="'$(Configuration)|$(Platform)'=='MinSizeRel|x64'">true</ExcludedFromBuild>
    </ClCompile>
    <ClInclude Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\include_RelWithDebInfo\ui_mainwindow.h" />
    <None Include="C:\Users\<USER>\Documents\testcase-viewer\build\testcase-viewer_autogen\autouic_RelWithDebInfo.stamp">
    </None>
  </ItemGroup>
  <ItemGroup>
    <ProjectReference Include="C:\Users\<USER>\Documents\testcase-viewer\build\ZERO_CHECK.vcxproj">
      <Project>{6EAA0E62-FC10-3C83-B875-EAB60F234180}</Project>
      <Name>ZERO_CHECK</Name>
      <ReferenceOutputAssembly>false</ReferenceOutputAssembly>
      <CopyToOutputDirectory>Never</CopyToOutputDirectory>
    </ProjectReference>
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ExtensionTargets">
  </ImportGroup>
</Project>