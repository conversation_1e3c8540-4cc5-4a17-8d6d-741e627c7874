#include "excelreader.h"
#include <QDebug>
#include <QFileInfo>
#include <stdexcept>

ExcelReader::ExcelReader()
    : m_isLoaded(false)
{
}

ExcelReader::~ExcelReader()
{
}

bool ExcelReader::readExcelFile(const QString &filePath)
{
    m_lastError.clear();
    m_testCases.clear();
    m_isLoaded = false;

    // 检查文件是否存在
    QFileInfo fileInfo(filePath);
    if (!fileInfo.exists()) {
        m_lastError = QString("文件不存在: %1").arg(filePath);
        return false;
    }

    try {
        // 打开Excel文件
        m_workbook.load(filePath.toStdString());
        m_worksheet = m_workbook.active_sheet();
        m_filePath = filePath;
        m_isLoaded = true;

        // 从第2行开始读取（第1行是标题）
        int row = 2;
        while (true) {
            // 检查是否到达空行
            if (isRowEmpty(row)) {
                break;
            }

            TestCase testCase = parseRow(row);
            if (!testCase.isEmpty()) {
                testCase.setRowNumber(row);
                m_testCases.append(testCase);
            }

            row++;

            // 防止无限循环，最多读取10000行
            if (row > 10000) {
                break;
            }
        }

        qDebug() << QString("成功读取 %1 个测试用例").arg(m_testCases.size());
        return true;
    }
    catch (const std::exception& e) {
        m_lastError = QString("读取Excel文件失败: %1").arg(e.what());
        return false;
    }
}

bool ExcelReader::saveExcelFile(const QString &filePath)
{
    if (!m_isLoaded) {
        m_lastError = "没有加载的Excel文件";
        return false;
    }

    m_lastError.clear();

    try {
        // 更新Excel文件中的测试结果
        for (const TestCase &testCase : m_testCases) {
            if (testCase.getRowNumber() > 0) {
                setCellValue(testCase.getRowNumber(), TEST_RESULT_COLUMN, testCase.getTestResultString());
            }
        }

        // 保存文件
        QString saveFilePath = filePath.isEmpty() ? m_filePath : filePath;
        m_workbook.save(saveFilePath.toStdString());

        qDebug() << QString("成功保存Excel文件: %1").arg(saveFilePath);
        return true;
    }
    catch (const std::exception& e) {
        m_lastError = QString("保存Excel文件失败: %1").arg(e.what());
        return false;
    }
}

void ExcelReader::updateTestCases(const QList<TestCase> &testCases)
{
    m_testCases = testCases;
}

TestCase ExcelReader::parseRow(int row)
{
    QString caseId = getCellValue(row, CASE_ID_COLUMN);
    QString testSteps = getCellValue(row, TEST_STEPS_COLUMN);
    QString expectedResult = getCellValue(row, EXPECTED_RESULT_COLUMN);
    QString testResultStr = getCellValue(row, TEST_RESULT_COLUMN);
    
    TestCase::TestResult testResult = TestCase::parseTestResult(testResultStr);
    
    return TestCase(caseId, testSteps, expectedResult, testResult);
}

bool ExcelReader::isRowEmpty(int row)
{
    // 检查关键列是否都为空
    QString caseId = getCellValue(row, CASE_ID_COLUMN);
    QString testSteps = getCellValue(row, TEST_STEPS_COLUMN);
    QString expectedResult = getCellValue(row, EXPECTED_RESULT_COLUMN);
    
    return caseId.trimmed().isEmpty() && 
           testSteps.trimmed().isEmpty() && 
           expectedResult.trimmed().isEmpty();
}

QString ExcelReader::getCellValue(int row, int col)
{
    if (!m_isLoaded) {
        return QString();
    }

    try {
        xlnt::cell_reference ref(col, row);
        if (m_worksheet.has_cell(ref)) {
            xlnt::cell cell = m_worksheet.cell(ref);
            return QString::fromStdString(cell.to_string()).trimmed();
        }
    }
    catch (const std::exception&) {
        // 忽略异常，返回空字符串
    }

    return QString();
}

void ExcelReader::setCellValue(int row, int col, const QString &value)
{
    if (m_isLoaded) {
        try {
            xlnt::cell_reference ref(col, row);
            xlnt::cell cell = m_worksheet.cell(ref);
            cell.value(value.toStdString());
        }
        catch (const std::exception&) {
            // 忽略异常
        }
    }
}

int ExcelReader::columnLetterToNumber(const QString &columnLetter)
{
    int result = 0;
    for (int i = 0; i < columnLetter.length(); i++) {
        result = result * 26 + (columnLetter[i].toUpper().toLatin1() - 'A' + 1);
    }
    return result;
}
